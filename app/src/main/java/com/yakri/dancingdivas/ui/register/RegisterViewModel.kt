package com.yakri.dancingdivas.ui.register

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class RegisterViewModel : ViewModel() {

    private val _name = MutableLiveData<String>()
    val name: LiveData<String> = _name

    private val _address = MutableLiveData<String>()
    val address: LiveData<String> = _address

    private val _phoneNumber = MutableLiveData<String>()
    val phoneNumber: LiveData<String> = _phoneNumber

    private val _age = MutableLiveData<String>()
    val age: LiveData<String> = _age

    private val _gender = MutableLiveData<String>()
    val gender: LiveData<String> = _gender

    private val _registrationStatus = MutableLiveData<String>()
    val registrationStatus: LiveData<String> = _registrationStatus

    fun updateName(name: String) {
        _name.value = name
    }

    fun updateAddress(address: String) {
        _address.value = address
    }

    fun updatePhoneNumber(phoneNumber: String) {
        _phoneNumber.value = phoneNumber
    }

    fun updateAge(age: String) {
        _age.value = age
    }

    fun updateGender(gender: String) {
        _gender.value = gender
    }

    fun register() {
        // TODO: Implement registration logic
        _registrationStatus.value = "Registration successful!"
    }

    fun isFormValid(): Boolean {
        return !_name.value.isNullOrBlank() &&
                !_address.value.isNullOrBlank() &&
                !_phoneNumber.value.isNullOrBlank() &&
                !_age.value.isNullOrBlank() &&
                !_gender.value.isNullOrBlank()
    }
}
