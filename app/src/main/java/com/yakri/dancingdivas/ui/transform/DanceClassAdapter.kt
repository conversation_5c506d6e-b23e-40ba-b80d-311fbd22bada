package com.yakri.dancingdivas.ui.transform

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.yakri.dancingdivas.data.DanceClass
import com.yakri.dancingdivas.databinding.ItemDanceClassBinding

class DanceClassAdapter(
    private val onBookClick: (DanceClass) -> Unit
) : ListAdapter<DanceClass, DanceClassAdapter.DanceClassViewHolder>(DanceClassDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DanceClassViewHolder {
        val binding = ItemDanceClassBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DanceClassViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DanceClassViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class DanceClassViewHolder(
        private val binding: ItemDanceClassBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(danceClass: DanceClass) {
            binding.apply {
                classTitle.text = danceClass.title
                classDescription.text = danceClass.description
                instructorName.text = danceClass.instructor
                classDuration.text = danceClass.duration
                classPrice.text = danceClass.price
                levelChip.text = danceClass.level
                classImage.setImageResource(danceClass.imageResource)

                // Set level chip color based on level
                when (danceClass.level.lowercase()) {
                    "beginner" -> levelChip.setChipBackgroundColorResource(android.R.color.holo_green_light)
                    "intermediate" -> levelChip.setChipBackgroundColorResource(android.R.color.holo_orange_light)
                    "advanced" -> levelChip.setChipBackgroundColorResource(android.R.color.holo_red_light)
                }

                bookButton.setOnClickListener {
                    onBookClick(danceClass)
                }
            }
        }
    }

    class DanceClassDiffCallback : DiffUtil.ItemCallback<DanceClass>() {
        override fun areItemsTheSame(oldItem: DanceClass, newItem: DanceClass): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DanceClass, newItem: DanceClass): Boolean {
            return oldItem == newItem
        }
    }
}
