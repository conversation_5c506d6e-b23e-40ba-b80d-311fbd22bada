<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:backgroundTint="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Class Image -->
        <ImageView
            android:id="@+id/class_image"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            android:scaleType="centerCrop"
            android:background="@drawable/feature_card_background"
            tools:src="@drawable/ic_music_note" />

        <!-- Class Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title and Level -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/class_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    tools:text="Ballet" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/level_chip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    tools:text="Beginner" />

            </LinearLayout>

            <!-- Description -->
            <TextView
                android:id="@+id/class_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_marginBottom="8dp"
                tools:text="Classical ballet technique for all levels. Focus on grace, posture, and fundamental movements." />

            <!-- Instructor and Duration -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/instructor_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:drawableStart="@drawable/ic_people"
                    android:drawablePadding="4dp"
                    tools:text="Sarah Johnson" />

                <TextView
                    android:id="@+id/class_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:drawableStart="@drawable/ic_schedule"
                    android:drawablePadding="4dp"
                    tools:text="60 min" />

            </LinearLayout>

            <!-- Price and Book Button -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/class_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/accent_color"
                    tools:text="$25/class" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/book_button"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="@string/book_class"
                    android:textSize="12sp"
                    android:backgroundTint="@color/accent_color"
                    android:textColor="@android:color/white"
                    app:cornerRadius="18dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
