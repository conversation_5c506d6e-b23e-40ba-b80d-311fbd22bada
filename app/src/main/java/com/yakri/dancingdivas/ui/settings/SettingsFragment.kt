package com.yakri.dancingdivas.ui.settings

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.yakri.dancingdivas.databinding.FragmentSettingsEnhancedBinding

class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsEnhancedBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsEnhancedBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupClickListeners()

        return root
    }

    private fun setupClickListeners() {
        binding.apply {
            editProfileItem.setOnClickListener {
                Toast.makeText(requireContext(), "Edit Profile clicked", Toast.LENGTH_SHORT).show()
                // TODO: Navigate to edit profile screen
            }

            themePreferenceItem.setOnClickListener {
                Toast.makeText(requireContext(), "Theme preference clicked", Toast.LENGTH_SHORT).show()
                // TODO: Show theme selection dialog
            }

            languagePreferenceItem.setOnClickListener {
                Toast.makeText(requireContext(), "Language preference clicked", Toast.LENGTH_SHORT).show()
                // TODO: Show language selection dialog
            }

            aboutAppItem.setOnClickListener {
                Toast.makeText(requireContext(), "About Dancing Divas", Toast.LENGTH_SHORT).show()
                // TODO: Show about dialog or navigate to about screen
            }

            contactUsItem.setOnClickListener {
                openEmailClient()
            }

            classRemindersSwitch.setOnCheckedChangeListener { _, isChecked ->
                Toast.makeText(
                    requireContext(),
                    if (isChecked) "Class reminders enabled" else "Class reminders disabled",
                    Toast.LENGTH_SHORT
                ).show()
                // TODO: Save preference
            }

            promotionalEmailsSwitch.setOnCheckedChangeListener { _, isChecked ->
                Toast.makeText(
                    requireContext(),
                    if (isChecked) "Promotional emails enabled" else "Promotional emails disabled",
                    Toast.LENGTH_SHORT
                ).show()
                // TODO: Save preference
            }
        }
    }

    private fun openEmailClient() {
        val intent = Intent(Intent.ACTION_SENDTO).apply {
            data = Uri.parse("mailto:<EMAIL>")
            putExtra(Intent.EXTRA_SUBJECT, "Dancing Divas App - Contact")
        }

        try {
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "No email app found", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}