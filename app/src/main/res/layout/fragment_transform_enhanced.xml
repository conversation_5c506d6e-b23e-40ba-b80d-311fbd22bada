<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.transform.TransformFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/classes_title"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/classes_subtitle"
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

        <!-- Filter Chips -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/filter_chip_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:singleSelection="true"
            app:selectionRequired="false">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="All Classes"
                android:checked="true"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_beginner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/beginner_level"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_intermediate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/intermediate_level"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_advanced"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/advanced_level"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

        </com.google.android.material.chip.ChipGroup>

        <!-- Classes RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/classes_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            tools:listitem="@layout/item_dance_class" />

    </LinearLayout>

</ScrollView>
