package com.yakri.dancingdivas.ui.slideshow

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.yakri.dancingdivas.data.GalleryRepository
import com.yakri.dancingdivas.databinding.FragmentSlideshowEnhancedBinding

class SlideshowFragment : Fragment() {

    private var _binding: FragmentSlideshowEnhancedBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSlideshowEnhancedBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupGallery()

        return root
    }

    private fun setupGallery() {
        // Setup performances horizontal RecyclerView
        val performanceAdapter = GalleryImageAdapter()
        binding.performancesRecyclerView.apply {
            adapter = performanceAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }
        performanceAdapter.submitList(GalleryRepository.getPerformances())

        // Setup class highlights grid RecyclerView
        val highlightsAdapter = GalleryGridAdapter()
        binding.classHighlightsRecyclerView.apply {
            adapter = highlightsAdapter
            layoutManager = LinearLayoutManager(context)
        }
        highlightsAdapter.submitList(GalleryRepository.getClassHighlights())

        // Setup achievements horizontal RecyclerView
        val achievementsAdapter = GalleryImageAdapter()
        binding.achievementsRecyclerView.apply {
            adapter = achievementsAdapter
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }
        achievementsAdapter.submitList(GalleryRepository.getAchievements())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}