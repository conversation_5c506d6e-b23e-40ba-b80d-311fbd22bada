package com.yakri.dancingdivas.ui.register

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.yakri.dancingdivas.R
import com.yakri.dancingdivas.databinding.FragmentRegisterBinding

class RegisterFragment : Fragment() {

    private var _binding: FragmentRegisterBinding? = null
    private val binding get() = _binding!!
    private lateinit var registerViewModel: RegisterViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        registerViewModel = ViewModelProvider(this)[RegisterViewModel::class.java]
        _binding = FragmentRegisterBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        setupObservers()

        return root
    }

    private fun setupUI() {
        // Setup gender dropdown
        val genderOptions = arrayOf(
            getString(R.string.gender_male),
            getString(R.string.gender_female),
            getString(R.string.gender_other),
            getString(R.string.gender_prefer_not_to_say)
        )
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, genderOptions)
        binding.genderDropdown.setAdapter(adapter)

        // Setup text change listeners
        binding.nameEditText.doAfterTextChanged { text ->
            registerViewModel.updateName(text.toString())
        }

        binding.addressEditText.doAfterTextChanged { text ->
            registerViewModel.updateAddress(text.toString())
        }

        binding.phoneEditText.doAfterTextChanged { text ->
            registerViewModel.updatePhoneNumber(text.toString())
        }

        binding.ageEditText.doAfterTextChanged { text ->
            registerViewModel.updateAge(text.toString())
        }

        binding.genderDropdown.setOnItemClickListener { _, _, _, _ ->
            registerViewModel.updateGender(binding.genderDropdown.text.toString())
        }

        // Setup click listeners
        binding.backButton.setOnClickListener {
            findNavController().navigateUp()
        }

        binding.registerButton.setOnClickListener {
            if (registerViewModel.isFormValid()) {
                registerViewModel.register()
            } else {
                Toast.makeText(requireContext(), "Please fill in all fields", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupObservers() {
        registerViewModel.registrationStatus.observe(viewLifecycleOwner) { status ->
            if (status.isNotEmpty()) {
                Toast.makeText(requireContext(), status, Toast.LENGTH_LONG).show()
                // Navigate back to home after successful registration
                findNavController().navigateUp()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
