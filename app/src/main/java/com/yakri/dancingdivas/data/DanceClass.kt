package com.yakri.dancingdivas.data

data class DanceClass(
    val id: Int,
    val title: String,
    val description: String,
    val level: String,
    val duration: String,
    val imageResource: Int,
    val instructor: String,
    val schedule: List<String>,
    val price: String
)

enum class DanceLevel {
    BEGINNER,
    INTERMEDIATE,
    ADVANCED
}

object DanceClassRepository {
    fun getAllClasses(): List<DanceClass> {
        return listOf(
            DanceClass(
                id = 1,
                title = "Ballet",
                description = "Classical ballet technique for all levels. Focus on grace, posture, and fundamental movements.",
                level = "Beginner",
                duration = "60 minutes",
                imageResource = android.R.drawable.ic_menu_gallery, // Placeholder
                instructor = "<PERSON>",
                schedule = listOf("Monday 6:00 PM", "Wednesday 6:00 PM", "Saturday 10:00 AM"),
                price = "$25/class"
            ),
            DanceClass(
                id = 2,
                title = "Hip Hop",
                description = "Urban dance styles with high energy moves. Perfect for building confidence and rhythm.",
                level = "Intermediate",
                duration = "60 minutes",
                imageResource = android.R.drawable.ic_menu_gallery,
                instructor = "<PERSON>",
                schedule = listOf("Tuesday 7:00 PM", "Thursday 7:00 PM", "Saturday 2:00 PM"),
                price = "$30/class"
            ),
            DanceClass(
                id = 3,
                title = "Contemporary",
                description = "Modern dance combining ballet and jazz. Express yourself through fluid movements.",
                level = "Intermediate",
                duration = "90 minutes",
                imageResource = android.R.drawable.ic_menu_gallery,
                instructor = "Sarah Johnson",
                schedule = listOf("Monday 7:30 PM", "Friday 6:00 PM"),
                price = "$35/class"
            ),
            DanceClass(
                id = 4,
                title = "Salsa",
                description = "Latin dance with passionate rhythms. Learn partner dancing and social skills.",
                level = "Beginner",
                duration = "60 minutes",
                imageResource = android.R.drawable.ic_menu_gallery,
                instructor = "Maria Santos",
                schedule = listOf("Wednesday 8:00 PM", "Friday 8:00 PM", "Sunday 4:00 PM"),
                price = "$28/class"
            ),
            DanceClass(
                id = 5,
                title = "Jazz",
                description = "Energetic dance style with sharp movements. Great for building strength and flexibility.",
                level = "Advanced",
                duration = "60 minutes",
                imageResource = android.R.drawable.ic_menu_gallery,
                instructor = "Emily Rodriguez",
                schedule = listOf("Tuesday 8:30 PM", "Thursday 8:30 PM"),
                price = "$32/class"
            )
        )
    }
}
