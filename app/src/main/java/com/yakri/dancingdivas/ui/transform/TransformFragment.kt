package com.yakri.dancingdivas.ui.transform

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.yakri.dancingdivas.R
import com.yakri.dancingdivas.data.DanceClass
import com.yakri.dancingdivas.data.DanceClassRepository
import com.yakri.dancingdivas.databinding.FragmentTransformEnhancedBinding

/**
 * Enhanced Fragment that shows dance classes with filtering capabilities
 */
class TransformFragment : Fragment() {

    private var _binding: FragmentTransformEnhancedBinding? = null
    private val binding get() = _binding!!

    private lateinit var danceClassAdapter: DanceClassAdapter
    private var allClasses: List<DanceClass> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTransformEnhancedBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupRecyclerView()
        setupFilterChips()
        loadClasses()

        return root
    }

    private fun setupRecyclerView() {
        danceClassAdapter = DanceClassAdapter { danceClass ->
            onBookClassClicked(danceClass)
        }

        binding.classesRecyclerView.apply {
            adapter = danceClassAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }

    private fun setupFilterChips() {
        binding.filterChipGroup.setOnCheckedStateChangeListener { _, checkedIds ->
            val filteredClasses = when {
                checkedIds.contains(R.id.chip_beginner) -> allClasses.filter { it.level == "Beginner" }
                checkedIds.contains(R.id.chip_intermediate) -> allClasses.filter { it.level == "Intermediate" }
                checkedIds.contains(R.id.chip_advanced) -> allClasses.filter { it.level == "Advanced" }
                else -> allClasses
            }
            danceClassAdapter.submitList(filteredClasses)
        }
    }

    private fun loadClasses() {
        allClasses = DanceClassRepository.getAllClasses()
        danceClassAdapter.submitList(allClasses)
    }

    private fun onBookClassClicked(danceClass: DanceClass) {
        Toast.makeText(
            requireContext(),
            "Booking ${danceClass.title} class with ${danceClass.instructor}",
            Toast.LENGTH_SHORT
        ).show()
        // TODO: Implement actual booking functionality
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}