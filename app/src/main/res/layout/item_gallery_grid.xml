<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:backgroundTint="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <!-- Image -->
        <ImageView
            android:id="@+id/grid_image"
            android:layout_width="120dp"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            tools:src="@drawable/dancingdivas_hero" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:id="@+id/grid_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="4dp"
                tools:text="Hip Hop Class Highlights" />

            <TextView
                android:id="@+id/grid_description"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:maxLines="3"
                android:ellipsize="end"
                tools:text="Amazing energy and moves from our hip hop students during their weekly class session." />

            <TextView
                android:id="@+id/grid_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="@color/accent_color"
                android:textStyle="bold"
                tools:text="March 2024" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
