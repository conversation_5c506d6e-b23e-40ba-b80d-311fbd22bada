package com.yakri.dancingdivas.ui.reflow

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.yakri.dancingdivas.data.Instructor
import com.yakri.dancingdivas.data.InstructorRepository
import com.yakri.dancingdivas.databinding.FragmentReflowEnhancedBinding

class ReflowFragment : Fragment() {

    private var _binding: FragmentReflowEnhancedBinding? = null
    private val binding get() = _binding!!

    private lateinit var instructorAdapter: InstructorAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentReflowEnhancedBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupRecyclerView()
        loadInstructors()

        return root
    }

    private fun setupRecyclerView() {
        instructorAdapter = InstructorAdapter { instructor ->
            onViewProfileClicked(instructor)
        }

        binding.instructorsRecyclerView.apply {
            adapter = instructorAdapter
            layoutManager = LinearLayoutManager(context)
        }
    }

    private fun loadInstructors() {
        val instructors = InstructorRepository.getAllInstructors()
        instructorAdapter.submitList(instructors)
    }

    private fun onViewProfileClicked(instructor: Instructor) {
        Toast.makeText(
            requireContext(),
            "Viewing profile for ${instructor.name}",
            Toast.LENGTH_SHORT
        ).show()
        // TODO: Navigate to instructor detail page
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}