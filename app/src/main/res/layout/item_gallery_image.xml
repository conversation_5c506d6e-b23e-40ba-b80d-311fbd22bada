<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="180dp"
    android:layout_marginEnd="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:backgroundTint="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Image -->
        <ImageView
            android:id="@+id/gallery_image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:scaleType="centerCrop"
            tools:src="@drawable/dancingdivas_hero" />

        <!-- Caption -->
        <TextView
            android:id="@+id/gallery_caption"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:textSize="12sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Ballet Performance 2024" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
