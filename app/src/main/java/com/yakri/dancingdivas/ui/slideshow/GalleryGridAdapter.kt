package com.yakri.dancingdivas.ui.slideshow

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.yakri.dancingdivas.data.GalleryItem
import com.yakri.dancingdivas.databinding.ItemGalleryGridBinding

class GalleryGridAdapter : ListAdapter<GalleryItem, GalleryGridAdapter.GalleryGridViewHolder>(GalleryGridDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GalleryGridViewHolder {
        val binding = ItemGalleryGridBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GalleryGridViewHolder(binding)
    }

    override fun onBindViewHolder(holder: GalleryGridViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class GalleryGridViewHolder(
        private val binding: ItemGalleryGridBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(galleryItem: GalleryItem) {
            binding.apply {
                gridImage.setImageResource(galleryItem.imageResource)
                gridTitle.text = galleryItem.title
                gridDescription.text = galleryItem.description ?: ""
                gridDate.text = galleryItem.date ?: ""
            }
        }
    }

    class GalleryGridDiffCallback : DiffUtil.ItemCallback<GalleryItem>() {
        override fun areItemsTheSame(oldItem: GalleryItem, newItem: GalleryItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: GalleryItem, newItem: GalleryItem): Boolean {
            return oldItem == newItem
        }
    }
}
