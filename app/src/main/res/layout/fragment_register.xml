<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF0E6F0"
    tools:context=".ui.register.RegisterFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <ImageView
                android:id="@+id/back_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:contentDescription="Back"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Dancing Divas"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center" />

        </LinearLayout>

        <!-- Registration Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:backgroundTint="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Title -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/register_title"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="32dp" />

                <!-- Name Field -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="#FFF8F5F0"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxStrokeWidth="0dp"
                    app:boxStrokeWidthFocused="0dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/name_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/name_hint"
                        android:inputType="textPersonName"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Address Field -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="#FFF8F5F0"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxStrokeWidth="0dp"
                    app:boxStrokeWidthFocused="0dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/address_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/address_hint"
                        android:inputType="textPostalAddress"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Phone Number Field -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="#FFF8F5F0"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxStrokeWidth="0dp"
                    app:boxStrokeWidthFocused="0dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/phone_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/phone_hint"
                        android:inputType="phone"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Age Field -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="#FFF8F5F0"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxStrokeWidth="0dp"
                    app:boxStrokeWidthFocused="0dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/age_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/age_hint"
                        android:inputType="number"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Gender Dropdown -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="#FFF8F5F0"
                    app:boxCornerRadiusTopStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxStrokeWidth="0dp"
                    app:boxStrokeWidthFocused="0dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">

                    <AutoCompleteTextView
                        android:id="@+id/gender_dropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/gender_hint"
                        android:inputType="none"
                        android:textSize="16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Register Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/register_button"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/register_button"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:backgroundTint="#FFE91E63"
                    app:cornerRadius="28dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
