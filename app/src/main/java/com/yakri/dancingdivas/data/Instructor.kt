package com.yakri.dancingdivas.data

data class Instructor(
    val id: Int,
    val name: String,
    val specialty: String,
    val bio: String,
    val yearsExperience: Int,
    val imageResource: Int,
    val achievements: List<String>,
    val socialMedia: SocialMedia?
)

data class SocialMedia(
    val instagram: String?,
    val facebook: String?,
    val youtube: String?
)

object InstructorRepository {
    fun getAllInstructors(): List<Instructor> {
        return listOf(
            Instructor(
                id = 1,
                name = "<PERSON>",
                specialty = "Ballet & Contemporary",
                bio = "Professional dancer with 15 years of experience. Trained at Royal Ballet School and performed with major companies worldwide.",
                yearsExperience = 15,
                imageResource = android.R.drawable.ic_menu_gallery, // Placeholder
                achievements = listOf(
                    "Royal Ballet School Graduate",
                    "Principal Dancer - National Ballet",
                    "Choreographer of the Year 2022",
                    "Featured in Dance Magazine"
                ),
                socialMedia = SocialMedia(
                    instagram = "@sarahjohnsonballet",
                    facebook = "Sarah Johnson Dance",
                    youtube = "SarahJohnsonDance"
                )
            ),
            Instructor(
                id = 2,
                name = "<PERSON>",
                specialty = "Hip Hop & Jazz",
                bio = "Award-winning choreographer specializing in urban dance styles. Has worked with top recording artists and dance crews.",
                yearsExperience = 12,
                imageResource = android.R.drawable.ic_menu_gallery,
                achievements = listOf(
                    "World Hip Hop Championship Winner",
                    "MTV Video Music Awards Choreographer",
                    "Dance Studio Owner",
                    "Celebrity Choreographer"
                ),
                socialMedia = SocialMedia(
                    instagram = "@emilyrodriguezdance",
                    facebook = "Emily <PERSON> Hip Hop",
                    youtube = "EmilyRodriguezOfficial"
                )
            ),
            Instructor(
                id = 3,
                name = "Maria Santos",
                specialty = "Salsa & Latin",
                bio = "International salsa champion with studios across three countries. Passionate about sharing Latin culture through dance.",
                yearsExperience = 18,
                imageResource = android.R.drawable.ic_menu_gallery,
                achievements = listOf(
                    "World Salsa Champion 2019",
                    "Latin Dance Congress Founder",
                    "International Judge",
                    "Cultural Ambassador"
                ),
                socialMedia = SocialMedia(
                    instagram = "@mariasantossalsa",
                    facebook = "Maria Santos Latin Dance",
                    youtube = "MariaSantosLatin"
                )
            )
        )
    }
}
