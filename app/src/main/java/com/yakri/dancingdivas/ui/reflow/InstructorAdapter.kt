package com.yakri.dancingdivas.ui.reflow

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.yakri.dancingdivas.R
import com.yakri.dancingdivas.data.Instructor
import com.yakri.dancingdivas.databinding.ItemInstructorBinding

class InstructorAdapter(
    private val onViewProfileClick: (Instructor) -> Unit
) : ListAdapter<Instructor, InstructorAdapter.InstructorViewHolder>(InstructorDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InstructorViewHolder {
        val binding = ItemInstructorBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return InstructorViewHolder(binding)
    }

    override fun onBindViewHolder(holder: InstructorViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class InstructorViewHolder(
        private val binding: ItemInstructorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(instructor: Instructor) {
            binding.apply {
                instructorName.text = instructor.name
                instructorSpecialty.text = instructor.specialty
                instructorBio.text = instructor.bio
                instructorExperience.text = root.context.getString(
                    R.string.years_experience,
                    instructor.yearsExperience
                )
                instructorImage.setImageResource(instructor.imageResource)

                // Setup achievements RecyclerView
                val achievementAdapter = AchievementAdapter()
                achievementsRecyclerView.apply {
                    adapter = achievementAdapter
                    layoutManager = LinearLayoutManager(context)
                }
                achievementAdapter.submitList(instructor.achievements)

                viewProfileButton.setOnClickListener {
                    onViewProfileClick(instructor)
                }
            }
        }
    }

    class InstructorDiffCallback : DiffUtil.ItemCallback<Instructor>() {
        override fun areItemsTheSame(oldItem: Instructor, newItem: Instructor): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Instructor, newItem: Instructor): Boolean {
            return oldItem == newItem
        }
    }
}
