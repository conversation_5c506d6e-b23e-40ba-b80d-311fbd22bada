<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.slideshow.SlideshowFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/gallery_title"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/gallery_subtitle"
                android:textSize="16sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

        </LinearLayout>

        <!-- Recent Performances Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/recent_performances"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/performances_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginBottom="24dp"
            android:orientation="horizontal"
            tools:listitem="@layout/item_gallery_image" />

        <!-- Class Highlights Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/class_highlights"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/class_highlights_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            tools:listitem="@layout/item_gallery_grid" />

        <!-- Student Achievements Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/student_achievements"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/achievements_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:orientation="horizontal"
            tools:listitem="@layout/item_gallery_image" />

    </LinearLayout>

</ScrollView>
