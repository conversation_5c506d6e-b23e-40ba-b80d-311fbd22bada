<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    android:backgroundTint="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Instructor Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Profile Image -->
            <androidx.cardview.widget.CardView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="40dp"
                app:cardElevation="4dp">

                <ImageView
                    android:id="@+id/instructor_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/avatar_sarah" />

            </androidx.cardview.widget.CardView>

            <!-- Name and Specialty -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/instructor_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp"
                    tools:text="Sarah Johnson" />

                <TextView
                    android:id="@+id/instructor_specialty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@color/accent_color"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp"
                    tools:text="Ballet &amp; Contemporary" />

                <TextView
                    android:id="@+id/instructor_experience"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:drawableStart="@drawable/ic_schedule"
                    android:drawablePadding="4dp"
                    tools:text="15 years experience" />

            </LinearLayout>

        </LinearLayout>

        <!-- Bio -->
        <TextView
            android:id="@+id/instructor_bio"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:lineSpacingExtra="2dp"
            android:layout_marginBottom="16dp"
            tools:text="Professional dancer with 15 years of experience. Trained at Royal Ballet School and performed with major companies worldwide." />

        <!-- Achievements -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Achievements"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/achievements_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:layout_marginBottom="16dp"
            tools:listitem="@layout/item_achievement" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/view_profile_button"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="@string/view_profile"
                android:textSize="12sp"
                android:backgroundTint="@color/accent_color"
                android:textColor="@android:color/white"
                app:cornerRadius="18dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
