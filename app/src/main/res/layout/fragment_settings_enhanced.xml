<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.settings.SettingsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/settings_title"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

        </LinearLayout>

        <!-- Profile Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/profile_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:id="@+id/edit_profile_item"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_people"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/edit_profile"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_back"
                        android:rotation="180"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Notifications Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/notifications_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Class Reminders -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_schedule"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/class_reminders"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/class_reminders_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

                <!-- Promotional Emails -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_music_note"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/promotional_emails"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/promotional_emails_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="false" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Preferences Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/preferences_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- Theme Preference -->
                <LinearLayout
                    android:id="@+id/theme_preference_item"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_settings_black_24dp"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/theme_preference"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Light"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginEnd="8dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_back"
                        android:rotation="180"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Language Preference -->
                <LinearLayout
                    android:id="@+id/language_preference_item"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_home_black_24dp"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/language_preference"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="English"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginEnd="8dp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_back"
                        android:rotation="180"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- About Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/about_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <!-- About App -->
                <LinearLayout
                    android:id="@+id/about_app_item"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_music_note"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/about_app"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_back"
                        android:rotation="180"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Version -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_settings_black_24dp"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/version"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.0.0"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- Contact Us -->
                <LinearLayout
                    android:id="@+id/contact_us_item"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_people"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/accent_color" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/contact_us"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_arrow_back"
                        android:rotation="180"
                        android:tint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
