package com.yakri.dancingdivas.data

data class GalleryItem(
    val id: Int,
    val title: String,
    val description: String? = null,
    val imageResource: Int,
    val date: String? = null,
    val type: GalleryType
)

enum class GalleryType {
    PERFORMANCE,
    CLASS_HIGHLIGHT,
    ACHIEVEMENT
}

object GalleryRepository {
    fun getPerformances(): List<GalleryItem> {
        return listOf(
            GalleryItem(
                id = 1,
                title = "Spring Ballet Recital",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "March 2024",
                type = GalleryType.PERFORMANCE
            ),
            GalleryItem(
                id = 2,
                title = "Hip Hop Showcase",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "February 2024",
                type = GalleryType.PERFORMANCE
            ),
            GalleryItem(
                id = 3,
                title = "Contemporary Evening",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "January 2024",
                type = GalleryType.PERFORMANCE
            ),
            GalleryItem(
                id = 4,
                title = "Salsa Night",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "December 2023",
                type = GalleryType.PERFORMANCE
            )
        )
    }

    fun getClassHighlights(): List<GalleryItem> {
        return listOf(
            GalleryItem(
                id = 5,
                title = "Ballet Fundamentals",
                description = "Students mastering basic positions and graceful movements in our beginner ballet class.",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "March 2024",
                type = GalleryType.CLASS_HIGHLIGHT
            ),
            GalleryItem(
                id = 6,
                title = "Hip Hop Energy",
                description = "Amazing energy and moves from our hip hop students during their weekly class session.",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "March 2024",
                type = GalleryType.CLASS_HIGHLIGHT
            ),
            GalleryItem(
                id = 7,
                title = "Contemporary Expression",
                description = "Students exploring emotional expression through contemporary dance movements.",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "February 2024",
                type = GalleryType.CLASS_HIGHLIGHT
            )
        )
    }

    fun getAchievements(): List<GalleryItem> {
        return listOf(
            GalleryItem(
                id = 8,
                title = "Competition Winners",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "March 2024",
                type = GalleryType.ACHIEVEMENT
            ),
            GalleryItem(
                id = 9,
                title = "Scholarship Recipients",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "February 2024",
                type = GalleryType.ACHIEVEMENT
            ),
            GalleryItem(
                id = 10,
                title = "Performance Awards",
                imageResource = android.R.drawable.ic_menu_gallery,
                date = "January 2024",
                type = GalleryType.ACHIEVEMENT
            )
        )
    }
}
